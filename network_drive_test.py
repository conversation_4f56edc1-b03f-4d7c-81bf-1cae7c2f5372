import os
import time

def test_network_drives():
    """测试网络驱动器连接"""
    print("=== 网络驱动器诊断工具 ===\n")
    
    # 测试的路径列表
    test_paths = [
        "Y:",
        "Y:\\",
        "Y:\\Image",
        "Y:\\Image\\客户机",
        "Y:\\主板出货"
    ]
    
    for path in test_paths:
        print(f"测试路径: {path}")
        
        # 检查路径是否存在
        exists = os.path.exists(path)
        print(f"  存在性: {'✓' if exists else '✗'}")
        
        if exists:
            # 检查是否可读
            readable = os.access(path, os.R_OK)
            print(f"  可读性: {'✓' if readable else '✗'}")
            
            # 检查是否是目录
            is_dir = os.path.isdir(path)
            print(f"  是目录: {'✓' if is_dir else '✗'}")
            
            if is_dir and readable:
                try:
                    # 尝试列举内容
                    start_time = time.time()
                    items = os.listdir(path)
                    elapsed = time.time() - start_time
                    print(f"  内容数量: {len(items)} 个项目")
                    print(f"  响应时间: {elapsed:.2f} 秒")
                    
                    # 显示前几个项目
                    if items:
                        print(f"  前几个项目: {items[:3]}")
                        
                except Exception as e:
                    print(f"  列举失败: {e}")
        
        print()  # 空行分隔
    
    # 检查当前工作目录
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查环境变量
    print("\n相关环境变量:")
    for var in ['PATH', 'USERPROFILE', 'COMPUTERNAME']:
        value = os.environ.get(var, '未设置')
        print(f"  {var}: {value}")

if __name__ == "__main__":
    test_network_drives()
    input("\n按Enter键退出...")
