import os
import subprocess
import sys

def check_network_drives():
    """检查当前网络驱动器映射"""
    print("=== 检查网络驱动器映射 ===")
    
    try:
        # 使用 net use 命令查看当前映射
        result = subprocess.run(['net', 'use'], capture_output=True, text=True, encoding='gbk')
        if result.returncode == 0:
            print("当前网络驱动器映射:")
            print(result.stdout)
        else:
            print("无法获取网络驱动器信息")
            print(result.stderr)
    except Exception as e:
        print(f"检查失败: {e}")

def map_network_drive():
    """映射网络驱动器"""
    print("\n=== 映射网络驱动器 ===")
    print("请提供网络路径信息:")
    
    # 获取网络路径
    network_path = input("请输入网络路径 (例如: \\\\192.168.1.100\\share 或 \\\\server\\folder): ")
    if not network_path:
        print("未输入网络路径")
        return False
    
    # 获取用户名和密码
    username = input("用户名 (可选，按Enter跳过): ")
    password = input("密码 (可选，按Enter跳过): ")
    
    try:
        # 构建映射命令
        cmd = ['net', 'use', 'Y:', network_path]
        if username:
            cmd.extend(['/user:' + username])
            if password:
                cmd.append(password)
        
        print(f"执行命令: {' '.join(cmd[:4])}...")  # 不显示密码
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='gbk')
        if result.returncode == 0:
            print("✓ 网络驱动器映射成功!")
            print(result.stdout)
            return True
        else:
            print("✗ 映射失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"映射过程出错: {e}")
        return False

def disconnect_drive():
    """断开网络驱动器"""
    print("\n=== 断开网络驱动器 ===")
    
    try:
        result = subprocess.run(['net', 'use', 'Y:', '/delete'], capture_output=True, text=True, encoding='gbk')
        if result.returncode == 0:
            print("✓ 网络驱动器已断开")
        else:
            print("断开失败或驱动器未映射")
            print(result.stderr)
    except Exception as e:
        print(f"断开过程出错: {e}")

def test_common_paths():
    """测试常见的本地路径"""
    print("\n=== 测试常见路径 ===")
    
    common_paths = [
        "C:\\Users\\<USER>\\Pictures",
        "C:\\Users\\<USER>\\Pictures",
        "D:\\Photos",
        "E:\\Photos",
        "C:\\Photos"
    ]
    
    print("检查本地可用路径:")
    for path in common_paths:
        if os.path.exists(path):
            print(f"✓ {path}")
        else:
            print(f"✗ {path}")

def main():
    """主函数"""
    print("=== 网络驱动器修复工具 ===\n")
    
    while True:
        print("\n请选择操作:")
        print("1. 检查当前网络驱动器映射")
        print("2. 映射 Y: 驱动器")
        print("3. 断开 Y: 驱动器")
        print("4. 测试常见本地路径")
        print("5. 退出")
        
        choice = input("\n请选择 (1-5): ")
        
        if choice == "1":
            check_network_drives()
        elif choice == "2":
            map_network_drive()
        elif choice == "3":
            disconnect_drive()
        elif choice == "4":
            test_common_paths()
        elif choice == "5":
            print("退出程序")
            break
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
