import os
import shutil
import re
import time
from pathlib import Path
from datetime import datetime

# 预设的目录配置
DIRECTORIES = {
    "1": {
        "name": "主板出货",
        "path": r"Y:\主板出货",
        "pattern": r'_(\d{8})',  # 匹配日期格式 YYYYMMDD
        "description": "主板出货照片 (格式: 序列号_用户_YYYYMMDDHHMMSS.jpg)"
    },
    "2": {
        "name": "客户机",
        "path": r"Y:\Image\客户机",
        "pattern": r'(\d{8})',  # 根据实际文件名格式调整
        "description": "客户机照片"
    },
    "3": {
        "name": "自定义目录",
        "path": "",  # 用户输入
        "pattern": r'(\d{8})',  # 默认模式
        "description": "自定义目录路径"
    }
}

def select_directory():
    """选择要处理的目录"""
    print("\n=== 选择要处理的目录 ===")
    for key, value in DIRECTORIES.items():
        print(f"{key}. {value['name']} - {value['description']}")
        if value['path']:
            print(f"   路径: {value['path']}")
    
    choice = input("\n请选择目录 (1-3): ")
    
    if choice in DIRECTORIES:
        selected = DIRECTORIES[choice]
        if choice == "3":  # 自定义目录
            custom_path = input("请输入自定义目录路径 (例: Y:\\其他目录): ")
            selected["path"] = custom_path
            
            # 让用户选择文件名日期格式
            print("\n请选择文件名中的日期格式:")
            print("1. YYYYMMDD (如: 20240601)")
            print("2. YYYY-MM-DD (如: 2024-06-01)")
            print("3. 其他格式")
            
            pattern_choice = input("选择格式 (1-3): ")
            if pattern_choice == "1":
                selected["pattern"] = r'(\d{8})'
            elif pattern_choice == "2":
                selected["pattern"] = r'(\d{4}-\d{2}-\d{2})'
            else:
                custom_pattern = input("请输入正则表达式模式 (例: r'(\\d{8})'): ")
                selected["pattern"] = custom_pattern
        
        return selected
    else:
        print("无效选择")
        return None

def create_year_month_folders(base_path):
    """创建年月文件夹结构"""
    print(f"在 {base_path} 创建年月文件夹结构...")
    for year in range(2023, 2026):  # 2023-2025
        year_path = os.path.join(base_path, str(year))
        if not os.path.exists(year_path):
            os.makedirs(year_path)
            print(f"创建文件夹: {year}")
        
        for month in range(1, 13):  # 01-12月
            month_path = os.path.join(year_path, f"{month:02d}")
            if not os.path.exists(month_path):
                os.makedirs(month_path)
                print(f"创建文件夹: {year}/{month:02d}")

def delete_2022_files(base_path):
    """删除2022年的所有文件"""
    print(f"开始删除 {base_path} 中的2022年文件...")
    deleted_count = 0
    
    try:
        for filename in os.listdir(base_path):
            if "2022" in filename and (filename.endswith('.jpg') or filename.endswith('.png')):
                file_path = os.path.join(base_path, filename)
                try:
                    os.remove(file_path)
                    deleted_count += 1
                    if deleted_count % 1000 == 0:
                        print(f"已删除 {deleted_count} 个2022年文件...")
                except Exception as e:
                    print(f"删除文件失败: {filename}, 错误: {e}")
    except Exception as e:
        print(f"访问目录失败: {e}")
    
    print(f"完成！共删除 {deleted_count} 个2022年文件")

def organize_photos_by_date(base_path, pattern):
    """按日期整理照片到对应文件夹"""
    print(f"开始按日期整理 {base_path} 中的照片...")
    print(f"使用日期匹配模式: {pattern}")
    moved_count = 0
    error_count = 0
    
    try:
        files = os.listdir(base_path)
        image_files = [f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
        total_files = len(image_files)
        print(f"发现 {total_files} 个图片文件需要处理")
        
        for filename in image_files:
            # 提取文件名中的日期
            match = re.search(pattern, filename)
            
            if match:
                date_str = match.group(1)
                
                # 处理不同的日期格式
                try:
                    if '-' in date_str:  # YYYY-MM-DD格式
                        date_parts = date_str.split('-')
                        year = date_parts[0]
                        month = date_parts[1]
                    else:  # YYYYMMDD格式
                        year = date_str[:4]
                        month = date_str[4:6]
                    
                    # 跳过2022年文件
                    if year == "2022":
                        continue
                    
                    # 构建目标路径
                    target_folder = os.path.join(base_path, year, month)
                    
                    # 确保目标文件夹存在
                    if not os.path.exists(target_folder):
                        os.makedirs(target_folder)
                    
                    # 移动文件
                    source_file = os.path.join(base_path, filename)
                    target_file = os.path.join(target_folder, filename)
                    
                    # 检查文件是否在根目录（避免移动已整理的文件）
                    if os.path.dirname(source_file) == base_path:
                        # 检查目标文件是否已存在
                        if os.path.exists(target_file):
                            print(f"文件已存在，跳过: {filename}")
                            continue
                            
                        shutil.move(source_file, target_file)
                        moved_count += 1
                        
                        if moved_count % 2000 == 0:
                            print(f"已移动 {moved_count}/{total_files} 个文件...")
                            
                except Exception as e:
                    print(f"处理日期失败: {filename}, 日期: {date_str}, 错误: {e}")
                    error_count += 1
            else:
                print(f"无法解析日期的文件: {filename}")
                error_count += 1
                
    except Exception as e:
        print(f"处理过程中出错: {e}")
    
    print(f"整理完成！成功移动: {moved_count} 个文件，错误: {error_count} 个")

def test_directory_access(base_path):
    """测试目录访问性能"""
    print(f"正在测试目录访问: {base_path}")

    # 基本存在性检查
    if not os.path.exists(base_path):
        return "目录不存在"

    # 权限检查
    if not os.access(base_path, os.R_OK):
        return "没有读取权限"

    try:
        # 尝试获取目录信息
        stat_info = os.stat(base_path)
        print(f"目录信息获取成功")

        # 尝试简单的listdir操作，但设置计数器避免无限等待
        print("开始列举目录内容...")
        start_time = time.time()

        # 使用生成器方式逐个读取，避免一次性加载所有文件
        try:
            items = os.listdir(base_path)
            count = len(items)
            elapsed = time.time() - start_time

            if elapsed > 30:  # 如果超过30秒
                return f"目录响应缓慢: {count} 个项目 (耗时: {elapsed:.2f}秒)"

            print(f"目录列举完成: {count} 个项目 (耗时: {elapsed:.2f}秒)")

            # 如果项目数量合理，进行类型分析
            if count <= 5000:
                image_count = 0
                folder_count = 0

                for item in items[:1000]:  # 只分析前1000个项目
                    if item.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                        image_count += 1
                    elif os.path.isdir(os.path.join(base_path, item)):
                        folder_count += 1

                sample_info = f" (样本分析: 图片 {image_count}, 文件夹 {folder_count})" if count > 1000 else ""
                return f"总计: {count} 个项目{sample_info} (耗时: {elapsed:.2f}秒)"
            else:
                return f"大型目录: {count} 个项目 (耗时: {elapsed:.2f}秒)"

        except OSError as e:
            return f"目录列举失败: {e}"

    except Exception as e:
        return f"访问测试失败: {e}"

def get_directory_stats(base_path):
    """获取目录统计信息"""
    return test_directory_access(base_path)

def main():
    """主函数"""
    print("=== NAS 照片整理工具 (多目录版) ===")
    
    # 选择目录
    selected_dir = select_directory()
    if not selected_dir:
        return
    
    source_path = selected_dir['path']
    pattern = selected_dir['pattern']
    
    print(f"\n当前选择: {selected_dir['name']}")
    print(f"路径: {source_path}")

    # 检查路径是否可访问
    print("正在检查路径可访问性...")
    if not os.path.exists(source_path):
        print("错误: 无法访问选择的路径")
        print("请确保:")
        print("1. 网络驱动器已正确映射")
        print("2. 路径输入正确")
        print("3. 有访问权限")
        return

    print("路径可访问！")
    print("注意: 跳过目录统计以避免大型目录扫描延迟")
    print("您可以在操作菜单中选择'查看目录统计信息'来获取详细信息")
    
    choice = input(f"""
请选择对 {selected_dir['name']} 的操作:
1. 只创建年月文件夹结构
2. 只删除2022年文件
3. 只整理现有照片到对应文件夹
4. 执行完整流程 (创建文件夹 + 删除2022 + 整理照片)
5. 查看目录统计信息
6. 返回目录选择
7. 退出

请输入选项 (1-7): """)
    
    if choice == "1":
        create_year_month_folders(source_path)
    elif choice == "2":
        confirm = input("确认删除所有2022年文件? (输入'YES'确认): ")
        if confirm == "YES":
            delete_2022_files(source_path)
        else:
            print("操作已取消")
    elif choice == "3":
        organize_photos_by_date(source_path, pattern)
    elif choice == "4":
        create_year_month_folders(source_path)
        confirm = input("确认删除所有2022年文件? (输入'YES'确认): ")
        if confirm == "YES":
            delete_2022_files(source_path)
        organize_photos_by_date(source_path, pattern)
    elif choice == "5":
        print(f"目录统计: {get_directory_stats(source_path)}")
        input("按Enter继续...")
        main()  # 返回菜单
    elif choice == "6":
        main()  # 重新开始
    elif choice == "7":
        print("退出程序")
    else:
        print("无效选项")

if __name__ == "__main__":
    main()